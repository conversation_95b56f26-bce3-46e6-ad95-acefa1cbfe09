import React from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import { Balloon } from '../../entities/BalloonEntity';

interface BalloonProps {
  balloon: Balloon;
  onPop?: () => void;
}

export const BalloonComponent: React.FC<BalloonProps> = ({ balloon, onPop }) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  const shimmer = useSharedValue(0);

  React.useEffect(() => {
    if (balloon.isPopped) {
      // Pop animation
      scale.value = withSpring(1.5, { damping: 10 });
      opacity.value = withTiming(0, { duration: 300 }, () => {
        if (onPop) {
          runOnJS(onPop)();
        }
      });
    }
  }, [balloon.isPopped]);

  // Special balloon shimmer effect
  React.useEffect(() => {
    if (balloon.type.special && !balloon.isPopped) {
      shimmer.value = withTiming(1, { duration: 1000 }, () => {
        shimmer.value = withTiming(0, { duration: 1000 });
      });
    }
  }, [balloon.type.special, balloon.isPopped]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const shimmerStyle = useAnimatedStyle(() => ({
    opacity: balloon.type.special ? shimmer.value * 0.3 : 0,
  }));

  if (balloon.isPopped && opacity.value === 0) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.balloon,
        {
          left: balloon.position.x - balloon.type.size / 2,
          top: balloon.position.y - balloon.type.size / 2,
          width: balloon.type.size,
          height: balloon.type.size,
          backgroundColor: balloon.type.color,
        },
        animatedStyle,
      ]}
    >
      {/* Balloon highlight */}
      <View style={[styles.highlight, {
        width: balloon.type.size * 0.3,
        height: balloon.type.size * 0.3,
      }]} />

      {/* Special balloon shimmer */}
      {balloon.type.special && (
        <Animated.View
          style={[
            styles.shimmer,
            {
              width: balloon.type.size,
              height: balloon.type.size,
            },
            shimmerStyle
          ]}
        />
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  balloon: {
    position: 'absolute',
    borderRadius: 1000, // Make it circular
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  highlight: {
    position: 'absolute',
    top: '20%',
    left: '25%',
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    borderRadius: 1000,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 1000,
  },
});
