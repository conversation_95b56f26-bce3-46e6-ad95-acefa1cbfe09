import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface LevelSelectScreenProps {
  currentWorld: number;
  unlockedLevels: number[];
  completedLevels: number[];
  onLevelSelect: (level: number) => void;
  onBackPress: () => void;
}

const LEVELS_PER_WORLD = 10;
const TOTAL_WORLDS = 5;

export const LevelSelectScreen: React.FC<LevelSelectScreenProps> = ({
  currentWorld,
  unlockedLevels,
  completedLevels,
  onLevelSelect,
  onBackPress,
}) => {
  const renderLevelNode = (level: number, index: number) => {
    const isUnlocked = unlockedLevels.includes(level);
    const isCompleted = completedLevels.includes(level);
    const isCurrentLevel = level === Math.max(...unlockedLevels);

    // Calculate position for zigzag path
    const row = Math.floor(index / 3);
    const col = index % 3;
    const isEvenRow = row % 2 === 0;
    const actualCol = isEvenRow ? col : 2 - col;
    
    const x = 50 + actualCol * 120;
    const y = 100 + row * 120;

    return (
      <View key={level} style={[styles.levelContainer, { left: x, top: y }]}>
        {/* Connection line to next level */}
        {index < LEVELS_PER_WORLD - 1 && (
          <View style={[
            styles.connectionLine,
            getConnectionLineStyle(index, isUnlocked)
          ]} />
        )}
        
        {/* Level circle */}
        <TouchableOpacity
          style={[
            styles.levelCircle,
            isCompleted && styles.completedLevel,
            isCurrentLevel && styles.currentLevel,
            !isUnlocked && styles.lockedLevel,
          ]}
          onPress={() => isUnlocked && onLevelSelect(level)}
          disabled={!isUnlocked}
        >
          <LinearGradient
            colors={
              isCompleted
                ? ['#FFD700', '#FFA500']
                : isCurrentLevel
                ? ['#4CAF50', '#45A049']
                : isUnlocked
                ? ['#2196F3', '#1976D2']
                : ['#9E9E9E', '#757575']
            }
            style={styles.levelGradient}
          >
            <Text style={[
              styles.levelText,
              !isUnlocked && styles.lockedText
            ]}>
              {isUnlocked ? level : '🔒'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        {/* Stars for completed levels */}
        {isCompleted && (
          <View style={styles.starsContainer}>
            <Text style={styles.star}>⭐</Text>
            <Text style={styles.star}>⭐</Text>
            <Text style={styles.star}>⭐</Text>
          </View>
        )}
      </View>
    );
  };

  const getConnectionLineStyle = (index: number, isUnlocked: boolean) => {
    const row = Math.floor(index / 3);
    const col = index % 3;
    const isEvenRow = row % 2 === 0;
    
    let rotation = 0;
    let width = 80;
    
    if (col === 2 && isEvenRow) {
      // End of even row - diagonal down
      rotation = 45;
      width = 100;
    } else if (col === 0 && !isEvenRow) {
      // End of odd row - diagonal down
      rotation = -45;
      width = 100;
    } else {
      // Horizontal connection
      rotation = 0;
      width = 80;
    }

    return {
      transform: [{ rotate: `${rotation}deg` }],
      width,
      backgroundColor: isUnlocked ? '#4CAF50' : '#BDBDBD',
    };
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#87CEEB', '#98D8E8', '#B0E0E6']}
        style={styles.background}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.worldTitle}>World {currentWorld}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Level path */}
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.pathContainer}
          showsVerticalScrollIndicator={false}
        >
          {Array.from({ length: LEVELS_PER_WORLD }, (_, index) => {
            const level = index + 1;
            return renderLevelNode(level, index);
          })}
        </ScrollView>

        {/* World progress */}
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            Progress: {completedLevels.length}/{LEVELS_PER_WORLD} levels completed
          </Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill,
                { width: `${(completedLevels.length / LEVELS_PER_WORLD) * 100}%` }
              ]}
            />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  backButtonText: {
    color: '#2E86AB',
    fontSize: 16,
    fontWeight: 'bold',
  },
  worldTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2E86AB',
    textAlign: 'center',
  },
  placeholder: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  pathContainer: {
    height: LEVELS_PER_WORLD * 40 + 200,
    position: 'relative',
  },
  levelContainer: {
    position: 'absolute',
    alignItems: 'center',
  },
  connectionLine: {
    position: 'absolute',
    height: 4,
    top: 30,
    left: 60,
    zIndex: 1,
  },
  levelCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 2,
  },
  levelGradient: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedLevel: {
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  currentLevel: {
    borderWidth: 3,
    borderColor: '#4CAF50',
  },
  lockedLevel: {
    opacity: 0.6,
  },
  levelText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  lockedText: {
    fontSize: 16,
  },
  starsContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  star: {
    fontSize: 12,
    marginHorizontal: 1,
  },
  progressContainer: {
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  progressText: {
    fontSize: 16,
    color: '#2E86AB',
    textAlign: 'center',
    marginBottom: 10,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
});
