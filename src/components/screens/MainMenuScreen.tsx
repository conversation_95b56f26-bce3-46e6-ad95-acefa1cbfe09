import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { gameStateManager, GameProgress } from '../../utils/GameStateManager';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MainMenuScreenProps {
  onStartGame: () => void;
  onShowInstructions: () => void;
  onShowWorldSelect: () => void;
  onShowLevelSelect: () => void;
}

export const MainMenuScreen: React.FC<MainMenuScreenProps> = ({
  onStartGame,
  onShowInstructions,
  onShowWorldSelect,
  onShowLevelSelect,
}) => {
  const [progress, setProgress] = useState<GameProgress | null>(null);

  useEffect(() => {
    loadProgress();
  }, []);

  const loadProgress = async () => {
    const gameProgress = await gameStateManager.loadProgress();
    setProgress(gameProgress);
  };

  const handleContinueGame = () => {
    if (progress) {
      const { world, level } = gameStateManager.getCurrentWorldAndLevel();
      if (world === 1 && level === 1) {
        onStartGame(); // Start first level directly
      } else {
        onShowLevelSelect(); // Go to level select for current world
      }
    }
  };

  const getProgressText = () => {
    if (!progress) return '';
    const stats = gameStateManager.getStatistics();
    return `${stats.totalLevelsCompleted}/${stats.totalLevelsAvailable} levels completed`;
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#87CEEB', '#98D8E8', '#B0E0E6']}
        style={styles.background}
      >
        {/* Title */}
        <View style={styles.titleContainer}>
          <Text style={styles.title}>🎈</Text>
          <Text style={styles.gameTitle}>Balloon Blast</Text>
          <Text style={styles.subtitle}>Saga</Text>
        </View>

        {/* Progress Display */}
        {progress && (
          <View style={styles.progressDisplay}>
            <Text style={styles.progressText}>{getProgressText()}</Text>
            <Text style={styles.currentLevelText}>
              Current: World {progress.currentWorld}, Level {progress.currentLevel}
            </Text>
          </View>
        )}

        {/* Menu Buttons */}
        <View style={styles.menuContainer}>
          <TouchableOpacity style={styles.menuButton} onPress={handleContinueGame}>
            <LinearGradient
              colors={['#4CAF50', '#45A049']}
              style={styles.buttonGradient}
            >
              <Text style={styles.buttonText}>
                {progress && progress.gamesPlayed > 0 ? '▶️ Continue' : '🎮 Start Game'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuButton} onPress={onShowWorldSelect}>
            <LinearGradient
              colors={['#FF9800', '#F57C00']}
              style={styles.buttonGradient}
            >
              <Text style={styles.buttonText}>🌍 Select World</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuButton} onPress={onShowLevelSelect}>
            <LinearGradient
              colors={['#9C27B0', '#7B1FA2']}
              style={styles.buttonGradient}
            >
              <Text style={styles.buttonText}>🎯 Select Level</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuButton} onPress={onShowInstructions}>
            <LinearGradient
              colors={['#2196F3', '#1976D2']}
              style={styles.buttonGradient}
            >
              <Text style={styles.buttonText}>📖 How to Play</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>Tap balloons to pop them!</Text>
          <Text style={styles.versionText}>v1.0.0</Text>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 50,
  },
  titleContainer: {
    alignItems: 'center',
    marginTop: 50,
  },
  title: {
    fontSize: 80,
    marginBottom: 10,
  },
  gameTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#2E86AB',
    textAlign: 'center',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#2E86AB',
    textAlign: 'center',
  },
  progressDisplay: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  progressText: {
    fontSize: 16,
    color: '#2E86AB',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  currentLevelText: {
    fontSize: 14,
    color: '#2E86AB',
    fontWeight: '600',
  },
  menuContainer: {
    width: '100%',
    paddingHorizontal: 40,
    alignItems: 'center',
  },
  menuButton: {
    width: '100%',
    marginBottom: 15,
    borderRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonGradient: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  footer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  footerText: {
    fontSize: 16,
    color: '#2E86AB',
    marginBottom: 5,
    fontStyle: 'italic',
  },
  versionText: {
    fontSize: 12,
    color: '#2E86AB',
    opacity: 0.7,
  },
});
