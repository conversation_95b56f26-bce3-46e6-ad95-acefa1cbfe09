import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface InstructionsScreenProps {
  onBackPress: () => void;
}

export const InstructionsScreen: React.FC<InstructionsScreenProps> = ({
  onBackPress,
}) => {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#87CEEB', '#98D8E8', '#B0E0E6']}
        style={styles.background}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>How to Play</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Instructions */}
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🎯 Objective</Text>
            <Text style={styles.sectionText}>
              Pop as many balloons as possible within 60 seconds to score points and advance through levels!
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🎈 Balloon Types</Text>
            <View style={styles.balloonType}>
              <View style={[styles.balloonExample, { backgroundColor: '#FF6B6B' }]} />
              <Text style={styles.balloonText}>Regular Balloons - 10 points</Text>
            </View>
            <View style={styles.balloonType}>
              <View style={[styles.balloonExample, { backgroundColor: '#C0C0C0' }]} />
              <Text style={styles.balloonText}>Silver Balloons - 25 points</Text>
            </View>
            <View style={styles.balloonType}>
              <View style={[styles.balloonExample, { backgroundColor: '#FFD700' }]} />
              <Text style={styles.balloonText}>Gold Balloons - 50 points</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🎮 Controls</Text>
            <Text style={styles.sectionText}>
              • Tap anywhere on the screen to pop balloons
              • Balloons spawn from the bottom and float upward
              • Pop balloons quickly to build combos for bonus points
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>🏆 Progression</Text>
            <Text style={styles.sectionText}>
              • Complete levels to unlock new ones
              • Finish all 10 levels in a world to unlock the next world
              • Each world has unique themes and challenges
              • Track your progress and high scores
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>⚡ Game Speed</Text>
            <Text style={styles.sectionText}>
              • Balloons spawn every 0.33 seconds (fast-paced!)
              • Balloons move 3x faster than normal
              • Quick reflexes required for high scores
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>💡 Tips</Text>
            <Text style={styles.sectionText}>
              • Focus on special balloons (silver/gold) for higher scores
              • Build combos by popping balloons quickly in succession
              • Watch for balloon patterns and plan your taps
              • Don't let too many balloons escape off the top!
            </Text>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>Good luck and have fun! 🎈</Text>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  backButtonText: {
    color: '#2E86AB',
    fontSize: 16,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2E86AB',
    textAlign: 'center',
  },
  placeholder: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 15,
    borderRadius: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E86AB',
    marginBottom: 10,
  },
  sectionText: {
    fontSize: 16,
    color: '#2E86AB',
    lineHeight: 24,
  },
  balloonType: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  balloonExample: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  balloonText: {
    fontSize: 16,
    color: '#2E86AB',
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 18,
    color: '#2E86AB',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
