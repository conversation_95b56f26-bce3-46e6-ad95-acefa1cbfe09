import { Balloon } from '../entities/BalloonEntity';
import { PhysicsSystem } from '../systems/PhysicsSystem';
import { InputSystem } from '../systems/InputSystem';
import { ScoringSystem } from '../systems/ScoringSystem';
import { SoundSystem } from '../systems/SoundSystem';
import { GameState, GAME_CONFIG } from '../types/GameTypes';

export class GameEngine {
  private balloons: Balloon[] = [];
  private physicsSystem: PhysicsSystem;
  private inputSystem: InputSystem;
  private scoringSystem: ScoringSystem;
  private soundSystem: SoundSystem;
  private isRunning: boolean = false;
  private lastSpawnTime: number = 0;
  private gameStartTime: number = 0;
  private onStateChange?: (state: GameState) => void;

  constructor(screenWidth: number, screenHeight: number) {
    this.physicsSystem = new PhysicsSystem(screenWidth, screenHeight);
    this.inputSystem = new InputSystem();
    this.scoringSystem = new ScoringSystem();
    this.soundSystem = new SoundSystem();

    // Set up touch handling
    this.inputSystem.addTouchHandler(this.handleTouch.bind(this));
  }

  public start(): void {
    this.isRunning = true;
    this.gameStartTime = Date.now();
    this.lastSpawnTime = Date.now();
    this.balloons = [];
    this.scoringSystem.reset();
    this.soundSystem.playStartSound();
    this.notifyStateChange();
  }

  public stop(): void {
    this.isRunning = false;
    this.soundSystem.playGameOverSound();
    this.notifyStateChange();
  }

  public update(deltaTime: number): void {
    if (!this.isRunning || !this.physicsSystem || !this.scoringSystem) return;

    // Clamp deltaTime to prevent huge jumps
    const clampedDeltaTime = Math.min(deltaTime, 0.1); // Max 100ms

    const currentTime = Date.now();

    // Spawn new balloons
    if (currentTime - this.lastSpawnTime > GAME_CONFIG.BALLOON_SPAWN_RATE) {
      try {
        this.balloons.push(this.physicsSystem.spawnBalloon());
        this.lastSpawnTime = currentTime;
      } catch (error) {
        console.warn('Error spawning balloon:', error);
      }
    }

    // Update physics
    try {
      this.physicsSystem.updateBalloons(this.balloons, clampedDeltaTime);
    } catch (error) {
      console.warn('Error updating physics:', error);
    }

    // Remove off-screen balloons
    try {
      this.balloons = this.physicsSystem.removeOffScreenBalloons(this.balloons);
    } catch (error) {
      console.warn('Error removing off-screen balloons:', error);
    }

    // Check game time
    const gameTime = currentTime - this.gameStartTime;
    if (gameTime > GAME_CONFIG.GAME_DURATION) {
      this.stop();
    }

    this.notifyStateChange();
  }

  private handleTouch(touch: { x: number; y: number }): void {
    const touchEvent = { ...touch, timestamp: Date.now() };
    const hitBalloon = this.inputSystem.checkBalloonHit(touchEvent, this.balloons);
    if (hitBalloon) {
      const previousCombo = this.scoringSystem.getCombo();
      hitBalloon.pop();
      this.scoringSystem.popBalloon(hitBalloon.toEntity());

      // Play sound effects
      const newCombo = this.scoringSystem.getCombo();
      if (newCombo > previousCombo && newCombo > 2) {
        this.soundSystem.playComboSound(newCombo);
      } else {
        this.soundSystem.playPopSound();
      }

      this.notifyStateChange();
    }
  }

  public handleTouchInput(x: number, y: number): void {
    this.inputSystem.handleTouch(x, y);
  }

  public getBalloons(): Balloon[] {
    return this.balloons;
  }

  public getCombo(): number {
    return this.scoringSystem.getCombo();
  }

  public getGameState(): GameState {
    const currentTime = Date.now();
    const gameTime = currentTime - this.gameStartTime;
    const timeRemaining = Math.max(0, GAME_CONFIG.GAME_DURATION - gameTime);

    return {
      score: this.scoringSystem.getScore(),
      level: 1, // For now, always level 1
      balloons: this.balloons.map(b => b.toEntity()),
      isPlaying: this.isRunning,
      timeRemaining,
    };
  }

  public setStateChangeCallback(callback: (state: GameState) => void): void {
    this.onStateChange = callback;
  }

  private notifyStateChange(): void {
    if (this.onStateChange) {
      this.onStateChange(this.getGameState());
    }
  }

  public updateScreenDimensions(width: number, height: number): void {
    this.physicsSystem.updateScreenDimensions(width, height);
  }
}
