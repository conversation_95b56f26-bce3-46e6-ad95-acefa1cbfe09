import AsyncStorage from '@react-native-async-storage/async-storage';

export interface GameProgress {
  currentWorld: number;
  currentLevel: number;
  unlockedWorlds: number[];
  unlockedLevels: { [world: number]: number[] };
  completedLevels: { [world: number]: number[] };
  completedWorlds: number[];
  highScores: { [world: number]: { [level: number]: number } };
  totalScore: number;
  gamesPlayed: number;
}

const STORAGE_KEY = 'balloon_blast_progress';
const LEVELS_PER_WORLD = 10;
const TOTAL_WORLDS = 5;

const DEFAULT_PROGRESS: GameProgress = {
  currentWorld: 1,
  currentLevel: 1,
  unlockedWorlds: [1],
  unlockedLevels: { 1: [1] },
  completedLevels: {},
  completedWorlds: [],
  highScores: {},
  totalScore: 0,
  gamesPlayed: 0,
};

export class GameStateManager {
  private static instance: GameStateManager;
  private progress: GameProgress = { ...DEFAULT_PROGRESS };

  private constructor() {}

  public static getInstance(): GameStateManager {
    if (!GameStateManager.instance) {
      GameStateManager.instance = new GameStateManager();
    }
    return GameStateManager.instance;
  }

  // Load progress from storage
  public async loadProgress(): Promise<GameProgress> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        this.progress = { ...DEFAULT_PROGRESS, ...JSON.parse(stored) };
      } else {
        this.progress = { ...DEFAULT_PROGRESS };
      }
      return this.progress;
    } catch (error) {
      console.warn('Failed to load game progress:', error);
      this.progress = { ...DEFAULT_PROGRESS };
      return this.progress;
    }
  }

  // Save progress to storage
  public async saveProgress(): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(this.progress));
    } catch (error) {
      console.warn('Failed to save game progress:', error);
    }
  }

  // Get current progress
  public getProgress(): GameProgress {
    return { ...this.progress };
  }

  // Complete a level
  public async completeLevel(world: number, level: number, score: number): Promise<void> {
    // Update completed levels
    if (!this.progress.completedLevels[world]) {
      this.progress.completedLevels[world] = [];
    }
    if (!this.progress.completedLevels[world].includes(level)) {
      this.progress.completedLevels[world].push(level);
    }

    // Update high score
    if (!this.progress.highScores[world]) {
      this.progress.highScores[world] = {};
    }
    if (!this.progress.highScores[world][level] || score > this.progress.highScores[world][level]) {
      this.progress.highScores[world][level] = score;
    }

    // Update total score and games played
    this.progress.totalScore += score;
    this.progress.gamesPlayed += 1;

    // Unlock next level
    const nextLevel = level + 1;
    if (nextLevel <= LEVELS_PER_WORLD) {
      if (!this.progress.unlockedLevels[world]) {
        this.progress.unlockedLevels[world] = [];
      }
      if (!this.progress.unlockedLevels[world].includes(nextLevel)) {
        this.progress.unlockedLevels[world].push(nextLevel);
      }
      this.progress.currentLevel = nextLevel;
    } else {
      // World completed, unlock next world
      if (!this.progress.completedWorlds.includes(world)) {
        this.progress.completedWorlds.push(world);
      }
      
      const nextWorld = world + 1;
      if (nextWorld <= TOTAL_WORLDS) {
        if (!this.progress.unlockedWorlds.includes(nextWorld)) {
          this.progress.unlockedWorlds.push(nextWorld);
        }
        if (!this.progress.unlockedLevels[nextWorld]) {
          this.progress.unlockedLevels[nextWorld] = [1];
        }
        this.progress.currentWorld = nextWorld;
        this.progress.currentLevel = 1;
      }
    }

    await this.saveProgress();
  }

  // Get unlocked levels for a world
  public getUnlockedLevels(world: number): number[] {
    return this.progress.unlockedLevels[world] || [];
  }

  // Get completed levels for a world
  public getCompletedLevels(world: number): number[] {
    return this.progress.completedLevels[world] || [];
  }

  // Check if level is unlocked
  public isLevelUnlocked(world: number, level: number): boolean {
    const unlockedLevels = this.getUnlockedLevels(world);
    return unlockedLevels.includes(level);
  }

  // Check if world is unlocked
  public isWorldUnlocked(world: number): boolean {
    return this.progress.unlockedWorlds.includes(world);
  }

  // Check if world is completed
  public isWorldCompleted(world: number): boolean {
    return this.progress.completedWorlds.includes(world);
  }

  // Get high score for a level
  public getHighScore(world: number, level: number): number {
    return this.progress.highScores[world]?.[level] || 0;
  }

  // Reset progress (for testing or new game)
  public async resetProgress(): Promise<void> {
    this.progress = { ...DEFAULT_PROGRESS };
    await this.saveProgress();
  }

  // Get current world and level
  public getCurrentWorldAndLevel(): { world: number; level: number } {
    return {
      world: this.progress.currentWorld,
      level: this.progress.currentLevel,
    };
  }

  // Set current world and level (for navigation)
  public async setCurrentWorldAndLevel(world: number, level: number): Promise<void> {
    if (this.isWorldUnlocked(world) && this.isLevelUnlocked(world, level)) {
      this.progress.currentWorld = world;
      this.progress.currentLevel = level;
      await this.saveProgress();
    }
  }

  // Get game statistics
  public getStatistics() {
    const totalLevelsCompleted = Object.values(this.progress.completedLevels)
      .reduce((sum, levels) => sum + levels.length, 0);
    
    const totalLevelsAvailable = TOTAL_WORLDS * LEVELS_PER_WORLD;
    const completionPercentage = (totalLevelsCompleted / totalLevelsAvailable) * 100;

    return {
      totalScore: this.progress.totalScore,
      gamesPlayed: this.progress.gamesPlayed,
      averageScore: this.progress.gamesPlayed > 0 ? this.progress.totalScore / this.progress.gamesPlayed : 0,
      totalLevelsCompleted,
      totalLevelsAvailable,
      completionPercentage,
      worldsCompleted: this.progress.completedWorlds.length,
      totalWorlds: TOTAL_WORLDS,
    };
  }
}

// Export singleton instance
export const gameStateManager = GameStateManager.getInstance();
