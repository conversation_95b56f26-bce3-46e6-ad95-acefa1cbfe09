import { Balloon } from '../entities/BalloonEntity';
import { GAME_CONFIG } from '../types/GameTypes';

export class PhysicsSystem {
  private screenWidth: number;
  private screenHeight: number;

  constructor(screenWidth: number, screenHeight: number) {
    this.screenWidth = screenWidth;
    this.screenHeight = screenHeight;
  }

  public updateBalloons(balloons: Balloon[], deltaTime: number): void {
    balloons.forEach(balloon => {
      if (!balloon.isPopped) {
        balloon.update(deltaTime);
        this.applyBoundaries(balloon);
      }
    });
  }

  private applyBoundaries(balloon: Balloon): void {
    // Keep balloons within horizontal boundaries
    if (balloon.position.x - balloon.radius < 0) {
      balloon.position.x = balloon.radius;
      balloon.velocity.x = Math.abs(balloon.velocity.x);
    } else if (balloon.position.x + balloon.radius > this.screenWidth) {
      balloon.position.x = this.screenWidth - balloon.radius;
      balloon.velocity.x = -Math.abs(balloon.velocity.x);
    }
  }

  public removeOffScreenBalloons(balloons: Balloon[]): Balloon[] {
    return balloons.filter(balloon => !balloon.isOffScreen(this.screenHeight));
  }

  public spawnBalloon(): Balloon {
    const x = Math.random() * (this.screenWidth - 60) + 30; // 30px margin
    const y = this.screenHeight + 30; // Start below screen
    return new Balloon(x, y);
  }

  public updateScreenDimensions(width: number, height: number): void {
    this.screenWidth = width;
    this.screenHeight = height;
  }
}
