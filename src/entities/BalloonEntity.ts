import { BalloonEntity, BalloonType, Position, Velocity, BALLOON_TYPES } from '../types/GameTypes';

export class Balloon {
  public id: string;
  public type: BalloonType;
  public position: Position;
  public velocity: Velocity;
  public isPopped: boolean;
  public createdAt: number;
  public radius: number;

  constructor(x: number, y: number, balloonType?: BalloonType) {
    this.id = `balloon_${Date.now()}_${Math.random()}`;
    this.type = balloonType || this.getRandomBalloonType();
    this.position = { x, y };
    this.velocity = { x: 0, y: -50 }; // Float upward
    this.isPopped = false;
    this.createdAt = Date.now();
    this.radius = this.type.size / 2;
  }

  private getRandomBalloonType(): BalloonType {
    const types = Object.values(BALLOON_TYPES);
    return types[Math.floor(Math.random() * types.length)];
  }

  public update(deltaTime: number): void {
    if (this.isPopped) return;

    // Update position based on velocity
    this.position.x += this.velocity.x * deltaTime;
    this.position.y += this.velocity.y * deltaTime;

    // Add slight horizontal wobble for realistic movement
    const wobble = Math.sin((Date.now() - this.createdAt) * 0.002) * 10;
    this.position.x += wobble * deltaTime;
  }

  public pop(): void {
    this.isPopped = true;
  }

  public isOffScreen(screenHeight: number): boolean {
    return this.position.y < -this.radius;
  }

  public containsPoint(x: number, y: number): boolean {
    const distance = Math.sqrt(
      Math.pow(x - this.position.x, 2) + Math.pow(y - this.position.y, 2)
    );
    return distance <= this.radius;
  }

  public toEntity(): BalloonEntity {
    return {
      id: this.id,
      type: this.type,
      position: this.position,
      velocity: this.velocity,
      isPopped: this.isPopped,
      createdAt: this.createdAt,
    };
  }
}
