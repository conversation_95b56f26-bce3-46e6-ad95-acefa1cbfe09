// Core game types and interfaces

export interface Position {
  x: number;
  y: number;
}

export interface Velocity {
  x: number;
  y: number;
}

export interface BalloonType {
  id: string;
  color: string;
  points: number;
  size: number;
  special?: boolean;
}

export interface BalloonEntity {
  id: string;
  type: BalloonType;
  position: Position;
  velocity: Velocity;
  isPopped: boolean;
  createdAt: number;
}

export interface GameState {
  score: number;
  level: number;
  balloons: BalloonEntity[];
  isPlaying: boolean;
  timeRemaining: number;
}

export interface TouchEvent {
  x: number;
  y: number;
  timestamp: number;
}

// Balloon types configuration
export const BALLOON_TYPES: { [key: string]: BalloonType } = {
  RED: {
    id: 'red',
    color: '#FF6B6B',
    points: 10,
    size: 60,
  },
  BLUE: {
    id: 'blue',
    color: '#4ECDC4',
    points: 10,
    size: 60,
  },
  GREEN: {
    id: 'green',
    color: '#45B7D1',
    points: 10,
    size: 60,
  },
  YELLOW: {
    id: 'yellow',
    color: '#FFA07A',
    points: 10,
    size: 60,
  },
};

export const GAME_CONFIG = {
  BALLOON_SPAWN_RATE: 1000, // milliseconds
  BALLOON_FLOAT_SPEED: 50, // pixels per second
  GAME_DURATION: 60000, // 60 seconds
  SCREEN_WIDTH: 375, // will be updated with actual screen dimensions
  SCREEN_HEIGHT: 812, // will be updated with actual screen dimensions
};
