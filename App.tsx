import { StatusBar } from 'expo-status-bar';
import { StyleSheet, View } from 'react-native';
import { GameBoard } from './src/components/game/GameBoard';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <GameBoard />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#87CEEB',
  },
});
